"use client"

import { useState, useEffect } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { invoiceFormSchema, type InvoiceFormData } from "@/lib/validations"
import { createInvoice, updateInvoice, getProjectsByClient } from "@/lib/api/invoices-client"
import { getAvailableClients } from "@/lib/api/projects-client"
import { Invoice } from "@/lib/types"
import { getCurrentUserIdOrFallback } from "@/lib/auth"
import { Loader2, Plus, Trash2 } from "lucide-react"

interface InvoiceFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  invoice?: Invoice
  onSuccess: () => void
  preselectedClientId?: string
  preselectedProjectId?: string
}

interface Client {
  id: string
  name: string
  company?: string
}

interface Project {
  id: string
  name: string
  status: string
}

export function InvoiceForm({ 
  open, 
  onOpenChange, 
  invoice, 
  onSuccess, 
  preselectedClientId,
  preselectedProjectId 
}: InvoiceFormProps) {
  const [loading, setLoading] = useState(false)
  const [clients, setClients] = useState<Client[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [loadingData, setLoadingData] = useState(true)
  const [selectedClientId, setSelectedClientId] = useState<string>("")
  const isEditing = !!invoice

  const form = useForm<InvoiceFormData>({
    resolver: zodResolver(invoiceFormSchema),
    defaultValues: {
      client_id: "",
      project_id: "",
      amount: 0,
      tax_amount: 0,
      total_amount: 0,
      status: "draft",
      due_date: "",
      paid_date: "",
      items: [{ id: "1", description: "", quantity: 1, rate: 0, amount: 0 }],
      notes: "",
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  })

  // Watch for changes in items to calculate totals
  const watchedItems = form.watch("items")
  const watchedTaxAmount = form.watch("tax_amount")

  // Calculate totals when items or tax change
  useEffect(() => {
    const subtotal = watchedItems.reduce((sum, item) => {
      const amount = (item.quantity || 0) * (item.rate || 0)
      return sum + amount
    }, 0)

    const taxAmount = watchedTaxAmount || 0
    const total = subtotal + taxAmount

    form.setValue("amount", subtotal)
    form.setValue("total_amount", total)

    // Update individual item amounts
    watchedItems.forEach((item, index) => {
      const amount = (item.quantity || 0) * (item.rate || 0)
      form.setValue(`items.${index}.amount`, amount)
    })
  }, [watchedItems, watchedTaxAmount, form])

  // Reset form when invoice data changes
  useEffect(() => {
    if (open) {
      const defaultItems = invoice?.items || [{ id: "1", description: "", quantity: 1, rate: 0, amount: 0 }]
      
      form.reset({
        client_id: invoice?.client_id || preselectedClientId || "",
        project_id: invoice?.project_id || preselectedProjectId || "",
        amount: invoice?.amount || 0,
        tax_amount: invoice?.tax_amount || 0,
        total_amount: invoice?.total_amount || 0,
        status: invoice?.status || "draft",
        due_date: invoice?.due_date || "",
        paid_date: invoice?.paid_date || "",
        items: defaultItems,
        notes: invoice?.notes || "",
      })

      if (invoice?.client_id || preselectedClientId) {
        setSelectedClientId(invoice?.client_id || preselectedClientId || "")
      }
    }
  }, [invoice, preselectedClientId, preselectedProjectId, open, form])

  // Load clients when form opens
  useEffect(() => {
    if (open) {
      loadFormData()
    }
  }, [open])

  // Load projects when client changes
  useEffect(() => {
    if (selectedClientId) {
      loadProjects(selectedClientId)
    } else {
      setProjects([])
    }
  }, [selectedClientId])

  const loadFormData = async () => {
    setLoadingData(true)
    try {
      const clientsData = await getAvailableClients()
      setClients(clientsData)
    } catch (error) {
      console.error("Failed to load form data:", error)
    } finally {
      setLoadingData(false)
    }
  }

  const loadProjects = async (clientId: string) => {
    try {
      const projectsData = await getProjectsByClient(clientId)
      setProjects(projectsData)
    } catch (error) {
      console.error("Failed to load projects:", error)
      setProjects([])
    }
  }

  const onSubmit = async (data: InvoiceFormData) => {
    setLoading(true)
    try {
      // Get current user ID or use fallback for development
      const currentUserId = await getCurrentUserIdOrFallback()

      const invoiceData = {
        ...data,
        created_by: currentUserId,
        // Convert empty date strings to null for PostgreSQL compatibility
        due_date: data.due_date && data.due_date.trim() !== "" ? data.due_date : null,
        paid_date: data.paid_date && data.paid_date.trim() !== "" ? data.paid_date : null,
        // Handle optional project_id
        project_id: data.project_id && data.project_id.trim() !== "" ? data.project_id : null,
      }

      if (isEditing) {
        await updateInvoice(invoice.id, invoiceData)
      } else {
        await createInvoice(invoiceData)
      }
      onSuccess()
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error("Failed to save invoice:", error)
      alert("Failed to save invoice. Please check the console for details.")
    } finally {
      setLoading(false)
    }
  }

  const addItem = () => {
    append({
      id: Date.now().toString(),
      description: "",
      quantity: 1,
      rate: 0,
      amount: 0,
    })
  }

  const removeItem = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Edit Invoice" : "Create New Invoice"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Update the invoice information below." 
              : "Fill in the details to create a new invoice."
            }
          </DialogDescription>
        </DialogHeader>

        {loadingData ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading form data...</span>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Client and Project Selection */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="client_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Client *</FormLabel>
                      <Select 
                        onValueChange={(value) => {
                          field.onChange(value)
                          setSelectedClientId(value)
                          // Reset project selection when client changes
                          form.setValue("project_id", "")
                        }} 
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select client" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {clients.map((client) => (
                            <SelectItem key={client.id} value={client.id}>
                              {client.name} {client.company && `(${client.company})`}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="project_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project (Optional)</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select project" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">No project</SelectItem>
                          {projects.map((project) => (
                            <SelectItem key={project.id} value={project.id}>
                              {project.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Invoice Items */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Invoice Items
                    <Button type="button" onClick={addItem} size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Item
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {fields.map((field, index) => (
                    <div key={field.id} className="grid grid-cols-12 gap-2 items-end">
                      <div className="col-span-5">
                        <FormField
                          control={form.control}
                          name={`items.${index}.description`}
                          render={({ field }) => (
                            <FormItem>
                              {index === 0 && <FormLabel>Description</FormLabel>}
                              <FormControl>
                                <Input placeholder="Item description" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="col-span-2">
                        <FormField
                          control={form.control}
                          name={`items.${index}.quantity`}
                          render={({ field }) => (
                            <FormItem>
                              {index === 0 && <FormLabel>Qty</FormLabel>}
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="col-span-2">
                        <FormField
                          control={form.control}
                          name={`items.${index}.rate`}
                          render={({ field }) => (
                            <FormItem>
                              {index === 0 && <FormLabel>Rate</FormLabel>}
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="col-span-2">
                        <FormField
                          control={form.control}
                          name={`items.${index}.amount`}
                          render={({ field }) => (
                            <FormItem>
                              {index === 0 && <FormLabel>Amount</FormLabel>}
                              <FormControl>
                                <Input
                                  type="number"
                                  readOnly
                                  value={field.value.toFixed(2)}
                                  className="bg-muted"
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="col-span-1">
                        {index === 0 && <div className="h-6"></div>}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeItem(index)}
                          disabled={fields.length === 1}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Tax, Totals, and Additional Fields */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="draft">Draft</SelectItem>
                            <SelectItem value="sent">Sent</SelectItem>
                            <SelectItem value="paid">Paid</SelectItem>
                            <SelectItem value="overdue">Overdue</SelectItem>
                            <SelectItem value="cancelled">Cancelled</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="due_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Due Date</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch("status") === "paid" && (
                    <FormField
                      control={form.control}
                      name="paid_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Paid Date</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>${form.watch("amount").toFixed(2)}</span>
                    </div>

                    <FormField
                      control={form.control}
                      name="tax_amount"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex justify-between items-center">
                            <FormLabel>Tax Amount:</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                className="w-24 text-right"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-between font-bold text-lg border-t pt-2">
                      <span>Total:</span>
                      <span>${form.watch("total_amount").toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional notes or payment terms"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={loading || loadingData}>
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isEditing ? "Update Invoice" : "Create Invoice"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  )
}
