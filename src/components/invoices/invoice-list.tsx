"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { StatusBadge } from "@/components/ui/status-badge"
import { getInvoices, searchInvoices, getInvoicesByStatus, deleteInvoice } from "@/lib/api/invoices-client"
import { InvoiceWithRelations } from "@/lib/types"
import { formatCurrency, type Currency } from "@/lib/utils/currency"
import { Search, Plus, MoreHorizontal, Edit, Trash2, Eye, Calendar, DollarSign, FileText, Loader2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { InvoiceForm } from "./invoice-form"

interface InvoiceListProps {
  preselectedClientId?: string
  preselectedProjectId?: string
}

export function InvoiceList({ preselectedClientId, preselectedProjectId }: InvoiceListProps) {
  const [invoices, setInvoices] = useState<InvoiceWithRelations[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [showInvoiceForm, setShowInvoiceForm] = useState(false)
  const [editingInvoice, setEditingInvoice] = useState<InvoiceWithRelations | undefined>()

  const fetchInvoices = useCallback(async () => {
    setLoading(true)
    try {
      let data: InvoiceWithRelations[]
      
      if (searchQuery.trim()) {
        data = await searchInvoices(searchQuery)
      } else if (statusFilter !== "all") {
        data = await getInvoicesByStatus(statusFilter)
      } else {
        data = await getInvoices()
      }
      
      setInvoices(data)
    } catch (error) {
      console.error("Failed to fetch invoices:", error)
    } finally {
      setLoading(false)
    }
  }, [searchQuery, statusFilter])

  useEffect(() => {
    fetchInvoices()
  }, [fetchInvoices])

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query)
  }, [])

  const handleStatusFilter = useCallback((status: string) => {
    setStatusFilter(status)
  }, [])

  const handleEdit = (invoice: InvoiceWithRelations) => {
    setEditingInvoice(invoice)
    setShowInvoiceForm(true)
  }

  const handleDelete = async (invoice: InvoiceWithRelations) => {
    if (window.confirm(`Are you sure you want to delete invoice ${invoice.invoice_number}?`)) {
      try {
        await deleteInvoice(invoice.id)
        fetchInvoices()
      } catch (error) {
        console.error("Failed to delete invoice:", error)
        alert("Failed to delete invoice. Please try again.")
      }
    }
  }

  const handleFormSuccess = () => {
    fetchInvoices()
    setEditingInvoice(undefined)
  }

  const handleFormClose = () => {
    setShowInvoiceForm(false)
    setEditingInvoice(undefined)
  }



  const formatInvoiceCurrency = (amount: number, currency?: Currency) => {
    return formatCurrency(amount, currency || 'IDR')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading invoices...</span>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Search and Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search invoices..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={handleStatusFilter}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="sent">Sent</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
        <Button onClick={() => setShowInvoiceForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Invoice
        </Button>
      </div>

      {/* Invoice List */}
      {invoices.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No invoices found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchQuery || statusFilter !== "all" 
                ? "Try adjusting your search or filter criteria."
                : "Create your first invoice to get started."
              }
            </p>
            <Button onClick={() => setShowInvoiceForm(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Invoice
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {invoices.map((invoice) => (
            <Card key={invoice.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold text-lg">
                        {invoice.invoice_number}
                      </h3>
                      <StatusBadge status={invoice.status} />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        <span className="font-medium text-foreground">
                          {formatInvoiceCurrency(invoice.total_amount, invoice.currency as Currency)}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>
                          Due: {invoice.due_date ? formatDate(invoice.due_date) : "No due date"}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span>
                          Client: {invoice.client?.name}
                          {invoice.client?.company && ` (${invoice.client.company})`}
                        </span>
                      </div>
                    </div>

                    {invoice.project && (
                      <div className="mt-2">
                        <Badge variant="outline" className="text-xs">
                          Project: {invoice.project.name}
                        </Badge>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <Link href={`/invoices/${invoice.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                    </Link>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEdit(invoice)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDelete(invoice)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Invoice Form Dialog */}
      <InvoiceForm
        open={showInvoiceForm}
        onOpenChange={handleFormClose}
        invoice={editingInvoice}
        onSuccess={handleFormSuccess}
        preselectedClientId={preselectedClientId}
        preselectedProjectId={preselectedProjectId}
      />
    </div>
  )
}
