"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { StatusBadge } from "@/components/ui/status-badge"
import { PageHeader } from "@/components/layout/page-header"
import { getInvoice, updateInvoiceStatus } from "@/lib/api/invoices-client"
import { InvoiceWithRelations, InvoiceStatus } from "@/lib/types"
import { formatCurrency, type Currency } from "@/lib/utils/currency"
import { ArrowLeft, Edit, Download, Send, CheckCircle, XCircle, Loader2 } from "lucide-react"
import { InvoiceForm } from "@/components/invoices/invoice-form"

export default function InvoiceDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [invoice, setInvoice] = useState<InvoiceWithRelations | null>(null)
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)

  const invoiceId = params.id as string

  useEffect(() => {
    if (invoiceId) {
      fetchInvoice()
    }
  }, [invoiceId])

  const fetchInvoice = async () => {
    setLoading(true)
    try {
      const data = await getInvoice(invoiceId)
      setInvoice(data)
    } catch (error) {
      console.error("Failed to fetch invoice:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (newStatus: InvoiceStatus) => {
    if (!invoice) return

    setUpdating(true)
    try {
      const paidDate = newStatus === "paid" ? new Date().toISOString().split('T')[0] : undefined
      await updateInvoiceStatus(invoice.id, newStatus, paidDate)
      fetchInvoice()
    } catch (error) {
      console.error("Failed to update invoice status:", error)
      alert("Failed to update invoice status. Please try again.")
    } finally {
      setUpdating(false)
    }
  }



  const formatInvoiceCurrency = (amount: number, currency?: Currency) => {
    return formatCurrency(amount, currency || 'IDR')
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading invoice...</span>
      </div>
    )
  }

  if (!invoice) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="text-center py-8">
          <h2 className="text-2xl font-bold mb-2">Invoice not found</h2>
          <p className="text-muted-foreground mb-4">
            The invoice you're looking for doesn't exist or has been deleted.
          </p>
          <Button onClick={() => router.push("/invoices")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Invoices
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <PageHeader
        title={`Invoice ${invoice.invoice_number}`}
        description={`Created on ${formatDate(invoice.created_at)}`}
      >
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.push("/invoices")}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button variant="outline" onClick={() => setShowEditForm(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Download PDF
          </Button>
        </div>
      </PageHeader>

      <div className="grid gap-6 md:grid-cols-3">
        {/* Main Invoice Details */}
        <div className="md:col-span-2 space-y-6">
          {/* Invoice Header */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-3">
                  Invoice {invoice.invoice_number}
                  <StatusBadge status={invoice.status} />
                </CardTitle>
                <div className="text-right">
                  <div className="text-2xl font-bold">
                    {formatInvoiceCurrency(invoice.total_amount, invoice.currency as Currency)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Due: {invoice.due_date ? formatDate(invoice.due_date) : "No due date"}
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold mb-2">Bill To:</h4>
                  <div className="text-sm space-y-1">
                    <div className="font-medium">{invoice.client?.name}</div>
                    {invoice.client?.company && (
                      <div>{invoice.client.company}</div>
                    )}
                    {invoice.client?.email && (
                      <div>{invoice.client.email}</div>
                    )}
                    {invoice.client?.phone && (
                      <div>{invoice.client.phone}</div>
                    )}
                  </div>
                </div>
                
                {invoice.project && (
                  <div>
                    <h4 className="font-semibold mb-2">Project:</h4>
                    <Badge variant="outline">
                      {invoice.project.name}
                    </Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Invoice Items */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-12 gap-4 text-sm font-medium text-muted-foreground border-b pb-2">
                  <div className="col-span-6">Description</div>
                  <div className="col-span-2 text-center">Quantity</div>
                  <div className="col-span-2 text-center">Rate</div>
                  <div className="col-span-2 text-right">Amount</div>
                </div>
                
                {invoice.items && Array.isArray(invoice.items) && invoice.items.map((item: any, index: number) => (
                  <div key={index} className="grid grid-cols-12 gap-4 text-sm">
                    <div className="col-span-6">{item.description}</div>
                    <div className="col-span-2 text-center">{item.quantity}</div>
                    <div className="col-span-2 text-center">{formatInvoiceCurrency(item.rate, invoice.currency as Currency)}</div>
                    <div className="col-span-2 text-right">{formatInvoiceCurrency(item.amount, invoice.currency as Currency)}</div>
                  </div>
                ))}
                
                <div className="border-t pt-4 space-y-2">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>{formatInvoiceCurrency(invoice.amount, invoice.currency as Currency)}</span>
                  </div>
                  {invoice.tax_amount > 0 && (
                    <div className="flex justify-between">
                      <span>Tax:</span>
                      <span>{formatInvoiceCurrency(invoice.tax_amount, invoice.currency as Currency)}</span>
                    </div>
                  )}
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total:</span>
                    <span>{formatInvoiceCurrency(invoice.total_amount, invoice.currency as Currency)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          {invoice.notes && (
            <Card>
              <CardHeader>
                <CardTitle>Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm whitespace-pre-wrap">{invoice.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar Actions */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {invoice.status === "draft" && (
                <Button 
                  className="w-full" 
                  onClick={() => handleStatusUpdate("sent")}
                  disabled={updating}
                >
                  <Send className="mr-2 h-4 w-4" />
                  Send Invoice
                </Button>
              )}
              
              {(invoice.status === "sent" || invoice.status === "overdue") && (
                <Button 
                  className="w-full" 
                  onClick={() => handleStatusUpdate("paid")}
                  disabled={updating}
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Mark as Paid
                </Button>
              )}
              
              {invoice.status !== "cancelled" && invoice.status !== "paid" && (
                <Button 
                  variant="outline" 
                  className="w-full" 
                  onClick={() => handleStatusUpdate("cancelled")}
                  disabled={updating}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Cancel Invoice
                </Button>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Invoice Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Created:</span>
                <span>{formatDate(invoice.created_at)}</span>
              </div>
              {invoice.due_date && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Due Date:</span>
                  <span>{formatDate(invoice.due_date)}</span>
                </div>
              )}
              {invoice.paid_date && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Paid Date:</span>
                  <span>{formatDate(invoice.paid_date)}</span>
                </div>
              )}
              {invoice.created_by_user && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Created By:</span>
                  <span>{invoice.created_by_user.full_name}</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Form Dialog */}
      <InvoiceForm
        open={showEditForm}
        onOpenChange={setShowEditForm}
        invoice={invoice}
        onSuccess={() => {
          fetchInvoice()
          setShowEditForm(false)
        }}
      />
    </div>
  )
}
