import { createClient as createSupabaseClient } from '@/lib/supabase/client'
import { CreateInvoiceData, UpdateInvoiceData } from '@/lib/types'

export async function getInvoices() {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('invoices')
    .select(`
      *,
      client:clients(id, name, company),
      project:projects(id, name),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch invoices: ${error.message}`)
  }
  
  return data
}

export async function getInvoice(id: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('invoices')
    .select(`
      *,
      client:clients(id, name, company, email, phone, address),
      project:projects(id, name, description),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .eq('id', id)
    .single()
  
  if (error) {
    throw new Error(`Failed to fetch invoice: ${error.message}`)
  }
  
  return data
}

export async function createInvoice(invoiceData: CreateInvoiceData) {
  const supabase = createSupabaseClient()
  
  // Generate invoice number
  const invoiceNumber = await generateInvoiceNumber()
  
  const { data, error } = await supabase
    .from('invoices')
    .insert({
      ...invoiceData,
      invoice_number: invoiceNumber,
    })
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to create invoice: ${error.message}`)
  }
  
  return data
}

export async function updateInvoice(id: string, invoiceData: UpdateInvoiceData) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('invoices')
    .update(invoiceData)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to update invoice: ${error.message}`)
  }
  
  return data
}

export async function deleteInvoice(id: string) {
  const supabase = createSupabaseClient()
  
  const { error } = await supabase
    .from('invoices')
    .delete()
    .eq('id', id)
  
  if (error) {
    throw new Error(`Failed to delete invoice: ${error.message}`)
  }
}

export async function updateInvoiceStatus(id: string, status: string, paidDate?: string) {
  const supabase = createSupabaseClient()
  
  const updateData: any = { status }
  if (status === 'paid' && paidDate) {
    updateData.paid_date = paidDate
  }
  
  const { data, error } = await supabase
    .from('invoices')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to update invoice status: ${error.message}`)
  }
  
  return data
}

// Generate unique invoice number
async function generateInvoiceNumber(): Promise<string> {
  const supabase = createSupabaseClient()
  
  // Get current year and month
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  
  // Get the count of invoices for this month
  const { count, error } = await supabase
    .from('invoices')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', `${year}-${month}-01`)
    .lt('created_at', `${year}-${String(parseInt(month) + 1).padStart(2, '0')}-01`)
  
  if (error) {
    throw new Error(`Failed to generate invoice number: ${error.message}`)
  }
  
  const invoiceNumber = `INV-${year}${month}-${String((count || 0) + 1).padStart(4, '0')}`
  return invoiceNumber
}

// Get projects by client for project selection
export async function getProjectsByClient(clientId: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('projects')
    .select('id, name, status')
    .eq('client_id', clientId)
    .in('status', ['planning', 'in_progress', 'review', 'completed'])
    .order('name', { ascending: true })
  
  if (error) {
    throw new Error(`Failed to fetch projects: ${error.message}`)
  }
  
  return data
}

// Search invoices
export async function searchInvoices(query: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('invoices')
    .select(`
      *,
      client:clients(id, name, company),
      project:projects(id, name)
    `)
    .or(`invoice_number.ilike.%${query}%,notes.ilike.%${query}%`)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to search invoices: ${error.message}`)
  }
  
  return data
}

// Get invoices by status
export async function getInvoicesByStatus(status: string) {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('invoices')
    .select(`
      *,
      client:clients(id, name, company),
      project:projects(id, name)
    `)
    .eq('status', status)
    .order('created_at', { ascending: false })
  
  if (error) {
    throw new Error(`Failed to fetch invoices by status: ${error.message}`)
  }
  
  return data
}
